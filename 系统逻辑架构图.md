# 书店管理系统 - 系统逻辑架构图（优化版）

## 架构设计说明

本架构图采用分层架构模式，明确了各层的技术选型和职责划分，支持微服务架构演进。

## 系统逻辑架构图

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   表示层 (Presentation Layer)                        │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Web前端应用    │  │   移动端应用     │  │   管理后台      │  │   第三方集成     │ │
│  │                │  │                │  │                │  │                │ │
│  │ • Vue.js 2.x    │  │ • H5响应式设计   │  │ • Vue.js 2.x    │  │ • 开放API接口   │ │
│  │ • Element UI    │  │ • 微信小程序     │  │ • Element UI    │  │ • Webhook回调   │ │
│  │ • Vue Router    │  │ • 支付宝小程序   │  │ • ECharts图表   │  │ • OAuth2认证    │ │
│  │ • Vuex状态管理  │  │ • 原生App(规划)  │  │ • 权限控制UI    │  │                │ │
│  │ • Axios HTTP    │  │ • PWA支持       │  │ • 数据导出功能   │  │                │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ RESTful API / JSON
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                 服务接口层 (Service Interface Layer)                 │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   API网关       │  │   认证授权服务   │  │   文件服务      │  │   通知服务      │ │
│  │                │  │                │  │                │  │                │ │
│  │ • 路由转发      │  │ • JWT Token     │  │ • 图片上传      │  │ • 邮件通知      │ │
│  │ • 负载均衡      │  │ • Spring Security│ • 文件存储      │  │ • 短信通知      │ │
│  │ • 限流熔断      │  │ • RBAC权限      │  │ • 图片处理      │  │ • 站内消息      │ │
│  │ • 监控日志      │  │ • 单点登录      │  │ • CDN加速       │  │ • 推送服务      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                            RESTful API 控制器层                                 │ │
│  │                                                                                 │ │
│  │  UserController  │  BookController  │  OrderController  │  CartController      │ │
│  │  TopicController │  FileController  │  AdminController  │  ReportController    │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ 方法调用 / 依赖注入
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              核心业务逻辑层 (Business Logic Layer)                   │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                                业务服务层 (Service)                              │ │
│  │                                                                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │ │
│  │  │ 用户管理服务 │  │ 商品管理服务 │  │ 订单管理服务 │  │ 购物车服务   │            │ │
│  │  │            │  │            │  │            │  │            │            │ │
│  │  │ UserService │  │ BookService │  │ OrderService│  │ CartService │            │ │
│  │  │ • 用户注册  │  │ • 商品CRUD  │  │ • 订单创建  │  │ • 购物车管理 │            │ │
│  │  │ • 登录认证  │  │ • 分类管理  │  │ • 状态流转  │  │ • 批量操作  │            │ │
│  │  │ • 权限验证  │  │ • 库存管理  │  │ • 支付处理  │  │ • 数据同步  │            │ │
│  │  │ • 信息管理  │  │ • 搜索推荐  │  │ • 物流跟踪  │  │            │            │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘            │ │
│  │                                                                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │ │
│  │  │ 推荐系统服务 │  │ 支付管理服务 │  │ 统计分析服务 │  │ 系统配置服务 │            │ │
│  │  │            │  │            │  │            │  │            │            │ │
│  │  │RecommendSvc │  │ PaymentSvc  │  │AnalyticsSvc │  │ ConfigSvc   │            │ │
│  │  │ • 个性推荐  │  │ • 支付集成  │  │ • 数据统计  │  │ • 参数配置  │            │ │
│  │  │ • 热门商品  │  │ • 退款处理  │  │ • 报表生成  │  │ • 系统监控  │            │ │
│  │  │ • 书单管理  │  │ • 对账管理  │  │ • 用户画像  │  │ • 缓存管理  │            │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘            │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                            │                                         │
│                                            ▼ 领域对象 / 业务规则                      │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                              业务管理层 (Manager/Domain)                         │ │
│  │                                                                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │ │
│  │  │ 用户域管理  │  │ 商品域管理  │  │ 订单域管理  │  │ 营销域管理   │            │ │
│  │  │            │  │            │  │            │  │            │            │ │
│  │  │UserManager  │  │BookManager  │  │OrderManager │  │MarketManager│            │ │
│  │  │ • 业务规则  │  │ • 库存策略  │  │ • 订单状态机│  │ • 促销规则  │            │ │
│  │  │ • 数据校验  │  │ • 价格策略  │  │ • 支付策略  │  │ • 优惠券    │            │ │
│  │  │ • 事件处理  │  │ • 分类策略  │  │ • 物流策略  │  │ • 会员等级  │            │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘            │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ MyBatis ORM / JDBC
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   数据访问层 (Data Access Layer)                     │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                                数据访问接口层 (DAO)                              │ │
│  │                                                                                 │ │
│  │  UserMapper    │  BookMapper    │  OrderMapper   │  CartMapper   │  TopicMapper │ │
│  │  AddressMapper │  CategoryMapper│  PaymentMapper │  LogMapper    │  ConfigMapper│ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                            │                                         │
│                                            ▼ SQL / NoSQL                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                                  缓存层 (Cache Layer)                            │ │
│  │                                                                                 │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │ │
│  │  │   Redis     │  │  本地缓存    │  │   分布式锁   │  │   会话存储   │            │ │
│  │  │            │  │            │  │            │  │            │            │ │
│  │  │ • 热点数据  │  │ • Caffeine  │  │ • Redisson  │  │ • Session   │            │ │
│  │  │ • 用户会话  │  │ • 配置缓存  │  │ • 防重复提交 │  │ • JWT Token │            │ │
│  │  │ • 购物车    │  │ • 字典数据  │  │ • 限流控制  │  │ • 用户状态  │            │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘            │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                            │
                                            ▼ TCP/IP 连接
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   数据存储层 (Data Storage Layer)                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   关系型数据库   │  │   NoSQL数据库   │  │   文件存储      │  │   搜索引擎      │ │
│  │                │  │                │  │                │  │                │ │
│  │ • MySQL 8.0     │  │ • Redis 6.0+    │  │ • 本地文件系统  │  │ • Elasticsearch │ │
│  │ • 主从复制      │  │ • MongoDB(规划) │  │ • 阿里云OSS     │  │ • 全文搜索      │ │
│  │ • 读写分离      │  │ • 文档存储      │  │ • 腾讯云COS     │  │ • 商品检索      │ │
│  │ • 分库分表      │  │ • 日志存储      │  │ • 七牛云存储    │  │ • 日志分析      │ │
│  │ • 数据备份      │  │ • 缓存数据      │  │ • CDN加速       │  │                │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                                数据库详细信息                                   │ │
│  │                                                                                 │ │
│  │  核心业务表：user, book, bookorder, orderdetail, cart, address                  │ │
│  │  分类管理表：booksort, booksortlist, publish                                    │ │
│  │  推荐系统表：recommend, newproduct, booktopic, subbooktopic                     │ │
│  │  系统管理表：expense, bookimg, 系统配置表, 操作日志表                            │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 跨层通信接口

### 1. 表示层 ↔ 服务接口层
- **通信方式**: RESTful API over HTTP/HTTPS
- **数据格式**: JSON
- **认证方式**: JWT Bearer Token
- **协议标准**: OpenAPI 3.0 规范

### 2. 服务接口层 ↔ 核心业务逻辑层
- **通信方式**: 方法调用 (Method Invocation)
- **依赖注入**: Spring IoC Container
- **事务管理**: Spring Transaction Management
- **异常处理**: 统一异常处理机制

### 3. 核心业务逻辑层 ↔ 数据访问层
- **ORM框架**: MyBatis
- **连接池**: Druid Connection Pool
- **事务控制**: @Transactional 注解
- **SQL优化**: 索引优化、查询优化

### 4. 数据访问层 ↔ 数据存储层
- **数据库连接**: JDBC / MySQL Connector
- **缓存连接**: Jedis / Lettuce Redis Client
- **文件访问**: 本地文件系统 / 云存储SDK
- **搜索连接**: Elasticsearch High Level REST Client

## 微服务演进支持

当前架构支持向微服务架构演进，可按以下方式拆分：

### 服务拆分策略
1. **用户服务** (User Service): 用户管理、认证授权
2. **商品服务** (Product Service): 商品管理、分类管理、库存管理
3. **订单服务** (Order Service): 订单处理、支付管理、物流跟踪
4. **购物车服务** (Cart Service): 购物车管理、会话管理
5. **推荐服务** (Recommendation Service): 个性化推荐、数据分析
6. **通知服务** (Notification Service): 消息通知、邮件短信
7. **文件服务** (File Service): 文件上传、图片处理、CDN管理

### 服务间通信
- **同步通信**: RESTful API / gRPC
- **异步通信**: 消息队列 (RabbitMQ / Apache Kafka)
- **服务发现**: Eureka / Consul / Nacos
- **配置中心**: Spring Cloud Config / Apollo
- **API网关**: Spring Cloud Gateway / Zuul

## 技术选型说明

### 前端技术栈
- **框架**: Vue.js 2.x (渐进式JavaScript框架)
- **UI组件**: Element UI (企业级UI组件库)
- **状态管理**: Vuex (集中式状态管理)
- **路由管理**: Vue Router (官方路由管理器)
- **HTTP客户端**: Axios (Promise based HTTP client)
- **构建工具**: Vue CLI + Webpack

### 后端技术栈
- **框架**: Spring Boot 2.7.18 (企业级Java框架)
- **安全框架**: Spring Security + JWT (认证授权)
- **ORM框架**: MyBatis (持久层框架)
- **数据库**: MySQL 8.0 (关系型数据库)
- **缓存**: Redis 6.0+ (内存数据库)
- **连接池**: Druid (数据库连接池)
- **构建工具**: Maven (项目管理工具)

### 基础设施
- **Web服务器**: Nginx (反向代理、负载均衡)
- **应用服务器**: 内嵌Tomcat (Spring Boot内置)
- **文件存储**: 本地文件系统 + 云存储(OSS/COS)
- **监控工具**: Spring Boot Actuator + Micrometer
- **日志管理**: Logback + ELK Stack (规划)

## 架构优势

1. **分层清晰**: 职责明确，便于维护和扩展
2. **技术成熟**: 采用主流稳定的技术栈
3. **扩展性强**: 支持水平扩展和微服务演进
4. **性能优化**: 多级缓存、数据库优化
5. **安全可靠**: 完善的安全机制和异常处理
6. **开发效率**: 规范的开发流程和工具链
