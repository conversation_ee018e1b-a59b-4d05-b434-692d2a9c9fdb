<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="29b5e42c-32c5-486a-b7ba-e6486ece6295" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zFiGT9QsoWuHFLMnSp7iccxPYc" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>",
    "settings.editor.selected.configurable": "database.data.views.appearance"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql",
      "redis"
    ]
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="29b5e42c-32c5-486a-b7ba-e6486ece6295" name="更改" comment="" />
      <created>1751333126208</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751333126208</updated>
    </task>
    <servers />
  </component>
</project>