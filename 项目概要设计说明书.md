# 书店管理系统项目概要设计说明书

## 1 引言

### 1.1 编写目的

本文档旨在详细描述书店管理系统的概要设计，为系统开发、测试、维护和后续扩展提供技术指导。文档面向系统架构师、开发工程师、测试工程师和项目管理人员，帮助相关人员全面理解系统的整体架构、技术选型、模块设计和实现方案。

### 1.2 背景

随着电子商务的快速发展和数字化转型的推进，传统书店需要建立完善的在线销售和管理平台。本书店管理系统是一个基于现代Web技术栈的全功能电商平台，采用前后端分离架构，集成了用户管理、商品管理、订单处理、库存管理等核心功能，旨在为中小型书店提供完整的数字化运营解决方案。

系统支持多角色权限管理，包括普通用户的浏览购买功能和管理员的后台管理功能，能够满足书店日常运营的各种需求。

### 1.3 定义

- **前后端分离**：前端负责用户界面展示和交互，后端负责业务逻辑处理和数据管理
- **RESTful API**：遵循REST架构风格的Web服务接口设计
- **JWT**：JSON Web Token，用于用户身份认证的无状态令牌
- **SPA**：Single Page Application，单页面应用程序
- **ORM**：Object-Relational Mapping，对象关系映射
- **RBAC**：Role-Based Access Control，基于角色的访问控制

### 1.4 参考资料

- Spring Boot官方文档
- Vue.js官方文档
- MySQL数据库设计规范
- RESTful API设计最佳实践
- JWT认证标准规范
- Element UI组件库文档

## 2 总体设计

### 2.1 简述

书店管理系统是一个基于Spring Boot + Vue.js + MySQL技术栈的现代化电商平台，采用前后端分离的架构模式。系统实现了完整的电商业务流程，包括用户注册登录、商品浏览搜索、购物车管理、订单处理、支付流程、库存管理等核心功能。

系统支持两种用户角色：普通用户可以进行商品浏览、购买、订单管理等操作；管理员用户可以进行商品管理、订单管理、用户管理、系统配置等后台管理操作。

### 2.2 架构设计

#### 2.2.1 系统逻辑架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        表示层 (Presentation Layer)            │
├─────────────────────────────────────────────────────────────┤
│  用户端界面           │  管理端界面           │  移动端界面      │
│  - 商品浏览          │  - 商品管理          │  - 响应式设计    │
│  - 购物车管理        │  - 订单管理          │  - 触摸优化      │
│  - 订单处理          │  - 用户管理          │  - 移动端适配    │
│  - 用户中心          │  - 数据统计          │                 │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        业务层 (Business Layer)               │
├─────────────────────────────────────────────────────────────┤
│  用户管理服务         │  商品管理服务         │  订单管理服务    │
│  - 用户认证          │  - 商品CRUD          │  - 订单创建      │
│  - 权限控制          │  - 分类管理          │  - 状态管理      │
│  - 个人信息管理      │  - 库存管理          │  - 支付处理      │
│                     │  - 图片管理          │  - 物流跟踪      │
├─────────────────────────────────────────────────────────────┤
│  购物车服务          │  推荐服务            │  统计分析服务    │
│  - 商品添加          │  - 个性化推荐        │  - 销售统计      │
│  - 数量管理          │  - 热门商品          │  - 用户分析      │
│  - 批量操作          │  - 新品推荐          │  - 库存报表      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据层 (Data Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  用户数据            │  商品数据            │  订单数据        │
│  - user              │  - book              │  - bookorder     │
│  - address           │  - bookimg           │  - orderdetail   │
│  - cart              │  - booksort          │  - expense       │
│                     │  - publish           │                 │
├─────────────────────────────────────────────────────────────┤
│  推荐数据            │  书单数据            │  系统数据        │
│  - recommend         │  - booktopic         │  - 日志数据      │
│  - newproduct        │  - subbooktopic      │  - 配置数据      │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2.2 系统物理架构图

```
┌─────────────────────────────────────────────────────────────┐
│                          客户端层                            │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器           │  移动浏览器          │  桌面应用        │
│  - Chrome            │  - Safari            │  - Electron      │
│  - Firefox           │  - Chrome Mobile     │  - PWA           │
│  - Safari            │  - 微信浏览器        │                 │
└─────────────────────────────────────────────────────────────┘
                                │ HTTP/HTTPS
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                          Web服务器层                         │
├─────────────────────────────────────────────────────────────┤
│  Nginx反向代理        │  静态资源服务        │  负载均衡        │
│  - 请求转发          │  - 图片服务          │  - 多实例部署    │
│  - SSL终端           │  - CSS/JS文件        │  - 健康检查      │
│  - 缓存控制          │  - 文件下载          │                 │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                          应用服务器层                        │
├─────────────────────────────────────────────────────────────┤
│  Spring Boot应用      │  JVM运行环境         │  监控管理        │
│  - RESTful API       │  - Java 17           │  - 应用监控      │
│  - 业务逻辑处理      │  - 内存管理          │  - 性能分析      │
│  - 安全认证          │  - 垃圾回收          │  - 日志管理      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                          数据服务层                          │
├─────────────────────────────────────────────────────────────┤
│  MySQL数据库         │  Redis缓存           │  文件存储        │
│  - 主从复制          │  - 会话缓存          │  - 图片文件      │
│  - 读写分离          │  - 数据缓存          │  - 文档文件      │
│  - 备份恢复          │  - 分布式锁          │  - 日志文件      │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2.3 顶层系统图

```
                    ┌─────────────────┐
                    │   用户终端设备   │
                    │  (PC/Mobile)    │
                    └─────────┬───────┘
                              │ HTTPS
                              ▼
                    ┌─────────────────┐
                    │   前端应用层     │
                    │   Vue.js SPA    │
                    └─────────┬───────┘
                              │ RESTful API
                              ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   安全网关层     │ │   应用服务层     │ │   文件服务层     │
│  Spring Security│ │  Spring Boot    │ │  Static Files   │
│  JWT认证        │ │  业务逻辑处理    │ │  图片上传下载    │
└─────────┬───────┘ └─────────┬───────┘ └─────────────────┘
          │                   │
          └─────────┬─────────┘
                    ▼
          ┌─────────────────┐
          │   数据访问层     │
          │   MyBatis ORM   │
          └─────────┬───────┘
                    │
          ┌─────────▼───────┐
          │   数据存储层     │
          │   MySQL数据库   │
          └─────────────────┘
```

#### 2.2.4 抽象业务流程图

```
用户注册登录流程：
┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐
│用户 │───▶│注册 │───▶│验证 │───▶│登录 │───▶│授权 │
│访问 │    │页面 │    │信息 │    │认证 │    │访问 │
└─────┘    └─────┘    └─────┘    └─────┘    └─────┘

商品购买流程：
┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐
│浏览 │───▶│选择 │───▶│加入 │───▶│确认 │───▶│支付 │───▶│完成 │
│商品 │    │商品 │    │购物车│    │订单 │    │订单 │    │购买 │
└─────┘    └─────┘    └─────┘    └─────┘    └─────┘    └─────┘

订单处理流程：
┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐
│订单 │───▶│库存 │───▶│支付 │───▶│发货 │───▶│物流 │───▶│完成 │
│创建 │    │扣减 │    │确认 │    │处理 │    │跟踪 │    │交易 │
└─────┘    └─────┘    └─────┘    └─────┘    └─────┘    └─────┘

管理员操作流程：
┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐    ┌─────┐
│管理员│───▶│权限 │───▶│管理 │───▶│数据 │───▶│系统 │
│登录 │    │验证 │    │操作 │    │更新 │    │维护 │
└─────┘    └─────┘    └─────┘    └─────┘    └─────┘
```

#### 2.2.5 子系统关系图

```
                    ┌─────────────────────────────────┐
                    │          用户管理子系统          │
                    │  - 用户注册登录                 │
                    │  - 权限控制                     │
                    │  - 个人信息管理                 │
                    └─────────────┬───────────────────┘
                                  │
                    ┌─────────────▼───────────────────┐
                    │          商品管理子系统          │
                    │  - 商品信息管理                 │
                    │  - 分类管理                     │
                    │  - 库存管理                     │
                    │  - 图片管理                     │
                    └─────────────┬───────────────────┘
                                  │
┌─────────────────┐ ┌─────────────▼───────────────────┐ ┌─────────────────┐
│  购物车子系统    │ │          订单管理子系统          │ │  推荐系统子系统  │
│  - 商品添加     │ │  - 订单创建处理                 │ │  - 个性化推荐   │
│  - 数量管理     │ │  - 状态跟踪                     │ │  - 热门商品     │
│  - 批量操作     │ │  - 支付集成                     │ │  - 新品推荐     │
└─────────┬───────┘ └─────────────┬───────────────────┘ └─────────┬───────┘
          │                       │                               │
          └───────────────────────┼───────────────────────────────┘
                                  │
                    ┌─────────────▼───────────────────┐
                    │          数据管理子系统          │
                    │  - 数据持久化                   │
                    │  - 事务管理                     │
                    │  - 缓存管理                     │
                    │  - 备份恢复                     │
                    └─────────────────────────────────┘
```

### 2.3 运行环境

#### 2.3.1 硬件环境
- **服务器配置**：
  - CPU: 4核心以上，推荐8核心
  - 内存: 8GB以上，推荐16GB
  - 存储: 100GB以上SSD硬盘
  - 网络: 100Mbps以上带宽

- **客户端配置**：
  - CPU: 双核心以上
  - 内存: 4GB以上
  - 浏览器: Chrome 70+, Firefox 65+, Safari 12+, Edge 79+

#### 2.3.2 软件环境
- **操作系统**：
  - 服务器: Linux (Ubuntu 18.04+, CentOS 7+) 或 Windows Server 2016+
  - 客户端: Windows 10+, macOS 10.14+, Linux桌面版

- **运行时环境**：
  - Java Runtime Environment: JDK 17+
  - Node.js: 14.0+
  - MySQL: 8.0+
  - Redis: 6.0+ (可选)

- **Web服务器**：
  - Nginx 1.18+ (生产环境推荐)
  - Apache HTTP Server 2.4+ (备选方案)

#### 2.3.3 开发环境
- **后端开发**：
  - IDE: IntelliJ IDEA 2021.3+ 或 Eclipse 2021-12+
  - 构建工具: Maven 3.6+
  - 版本控制: Git 2.30+

- **前端开发**：
  - IDE: Visual Studio Code 1.60+ 或 WebStorm 2021.3+
  - 包管理器: npm 7.0+ 或 yarn 1.22+
  - 构建工具: Vue CLI 4.5+

### 2.4 结构

#### 2.4.1 软件结构图

```
书店管理系统
├── bookstore-server (后端服务)
│   ├── src/main/java/com/huang/store
│   │   ├── controller (控制器层)
│   │   │   ├── UserController.java
│   │   │   ├── BookController.java
│   │   │   ├── OrderController.java
│   │   │   ├── CartController.java
│   │   │   └── TopicController.java
│   │   ├── service (业务逻辑层)
│   │   │   ├── UserService.java
│   │   │   ├── BookService.java
│   │   │   ├── OrderService.java
│   │   │   ├── CartService.java
│   │   │   └── TopicService.java
│   │   ├── mapper (数据访问层)
│   │   │   ├── UserMapper.java
│   │   │   ├── BookMapper.java
│   │   │   ├── OrderMapper.java
│   │   │   ├── CartMapper.java
│   │   │   └── TopicMapper.java
│   │   ├── entity (实体类)
│   │   │   ├── User.java
│   │   │   ├── Book.java
│   │   │   ├── Order.java
│   │   │   ├── Cart.java
│   │   │   └── Address.java
│   │   ├── security (安全配置)
│   │   │   ├── SecurityConfig.java
│   │   │   ├── JwtAuthenticationTokenFilter.java
│   │   │   └── CustomAuthenticationFilter.java
│   │   ├── util (工具类)
│   │   │   ├── JwtTokenUtil.java
│   │   │   ├── ValidationUtil.java
│   │   │   └── ResultUtil.java
│   │   └── exception (异常处理)
│   │       ├── GlobalExceptionHandler.java
│   │       └── BusinessException.java
│   └── src/main/resources
│       ├── database (数据库脚本)
│       ├── static (静态资源)
│       └── application.yml (配置文件)
└── bookstore-client (前端应用)
    ├── src
    │   ├── components (组件)
    │   │   ├── common (通用组件)
    │   │   ├── user (用户相关组件)
    │   │   ├── book (图书相关组件)
    │   │   └── admin (管理员组件)
    │   ├── views (页面视图)
    │   │   ├── Index.vue (首页)
    │   │   ├── Login.vue (登录页)
    │   │   ├── Register.vue (注册页)
    │   │   ├── Book.vue (图书详情)
    │   │   ├── Cart.vue (购物车)
    │   │   └── UserHome.vue (用户中心)
    │   ├── router (路由配置)
    │   ├── store (状态管理)
    │   ├── utils (工具函数)
    │   └── assets (静态资源)
    ├── static (静态文件)
    └── package.json (依赖配置)
```

#### 2.4.2 模块命名规则

**后端模块命名规则：**
- **包命名**：采用域名反向命名法，如 `com.huang.store.controller`
- **类命名**：采用大驼峰命名法（PascalCase），如 `UserController`、`BookService`
- **方法命名**：采用小驼峰命名法（camelCase），如 `getUserById`、`addToCart`
- **常量命名**：采用全大写字母加下划线，如 `MAX_PAGE_SIZE`、`DEFAULT_TIMEOUT`
- **变量命名**：采用小驼峰命名法，如 `userId`、`bookList`

**前端模块命名规则：**
- **组件命名**：采用大驼峰命名法，如 `UserProfile.vue`、`BookList.vue`
- **页面命名**：采用大驼峰命名法，如 `Index.vue`、`Login.vue`
- **方法命名**：采用小驼峰命名法，如 `handleLogin`、`fetchBookList`
- **变量命名**：采用小驼峰命名法，如 `userInfo`、`bookData`
- **CSS类命名**：采用BEM命名规范，如 `.book-card__title`、`.user-form--disabled`

**数据库命名规则：**
- **表名**：采用小写字母加下划线，如 `user`、`book_order`
- **字段名**：采用小驼峰命名法，如 `userId`、`bookName`
- **索引名**：采用 `idx_` 前缀，如 `idx_user_account`、`idx_book_isbn`
- **外键名**：采用 `fk_` 前缀，如 `fk_order_user`、`fk_cart_book`

#### 2.4.3 模块描述

**用户管理模块 (User Management)**
- **功能职责**：负责用户注册、登录、权限控制、个人信息管理等功能
- **核心类**：UserController、UserService、UserMapper、User实体类
- **主要接口**：用户注册、用户登录、获取用户信息、修改用户信息、用户权限验证
- **依赖关系**：依赖安全模块进行身份认证，为其他模块提供用户信息服务

**商品管理模块 (Book Management)**
- **功能职责**：负责图书信息管理、分类管理、库存管理、图片上传等功能
- **核心类**：BookController、BookService、BookMapper、Book实体类
- **主要接口**：图书增删改查、分类管理、库存更新、图片上传、商品搜索
- **依赖关系**：为购物车模块和订单模块提供商品信息服务

**订单管理模块 (Order Management)**
- **功能职责**：负责订单创建、状态跟踪、支付处理、物流管理等功能
- **核心类**：OrderController、OrderService、OrderMapper、Order实体类
- **主要接口**：订单创建、订单查询、状态更新、支付处理、物流跟踪
- **依赖关系**：依赖用户模块获取用户信息，依赖商品模块获取商品信息和更新库存

**购物车模块 (Shopping Cart)**
- **功能职责**：负责购物车商品管理、数量控制、批量操作等功能
- **核心类**：CartController、CartService、CartMapper、Cart实体类
- **主要接口**：添加商品、删除商品、修改数量、清空购物车、获取购物车列表
- **依赖关系**：依赖用户模块进行用户验证，依赖商品模块获取商品信息

**推荐系统模块 (Recommendation System)**
- **功能职责**：负责个性化推荐、热门商品推荐、新品推荐等功能
- **核心类**：RecommendService、TopicController、TopicService
- **主要接口**：获取推荐商品、热门商品列表、新品列表、书单管理
- **依赖关系**：依赖商品模块获取商品信息，依赖用户模块获取用户偏好

**安全模块 (Security Module)**
- **功能职责**：负责身份认证、权限控制、JWT令牌管理、安全配置等功能
- **核心类**：SecurityConfig、JwtAuthenticationTokenFilter、JwtTokenUtil
- **主要接口**：用户认证、权限验证、令牌生成、令牌验证
- **依赖关系**：为所有需要权限控制的模块提供安全服务

### 2.5 人工处理过程

#### 2.5.1 系统管理员处理过程
- **用户管理**：手动审核用户注册申请，处理用户账号异常，管理用户权限
- **商品管理**：审核商品信息，处理商品上下架，管理商品分类和库存
- **订单处理**：处理异常订单，协调退款退货，管理物流信息
- **系统维护**：监控系统运行状态，处理系统异常，执行数据备份

#### 2.5.2 客服人员处理过程
- **用户咨询**：回答用户关于商品、订单、支付等方面的问题
- **订单协调**：协助用户处理订单问题，联系物流公司跟踪包裹
- **售后服务**：处理用户投诉，协调退换货流程
- **数据录入**：录入客服处理记录，更新用户反馈信息

#### 2.5.3 运营人员处理过程
- **内容管理**：更新网站公告，管理推荐商品，制作营销活动
- **数据分析**：分析销售数据，制作运营报表，优化商品推荐策略
- **活动策划**：策划促销活动，设置优惠券，管理会员等级
- **商品运营**：优化商品描述，调整商品价格，管理商品分类

### 2.6 尚未解决的问题

#### 2.6.1 技术层面问题
- **分布式事务处理**：当前系统在单机环境下运行，未来扩展到分布式环境时需要解决分布式事务一致性问题
- **高并发处理**：系统在高并发场景下的性能优化和稳定性保障需要进一步完善
- **数据同步**：多数据源之间的数据同步机制和一致性保障需要优化
- **缓存策略**：Redis缓存的使用策略和缓存更新机制需要进一步完善

#### 2.6.2 业务层面问题
- **支付集成**：目前系统未集成真实的支付接口，需要接入第三方支付平台
- **物流跟踪**：物流信息的实时跟踪和状态更新需要与物流公司API对接
- **库存预警**：智能库存预警和自动补货机制需要进一步完善
- **个性化推荐**：推荐算法的准确性和实时性需要持续优化

#### 2.6.3 运维层面问题
- **监控告警**：系统监控和告警机制需要进一步完善
- **日志管理**：日志收集、分析和存储策略需要优化
- **备份恢复**：数据备份和灾难恢复方案需要制定和测试
- **性能调优**：数据库性能调优和应用性能优化需要持续进行

## 3 接口设计

### 3.1 用户接口

#### 3.1.1 Web用户界面
系统采用响应式设计，支持PC端和移动端访问，主要用户界面包括：

**用户端界面：**
- **首页界面**：展示推荐图书、热门图书、新品图书，提供搜索功能
- **登录注册界面**：用户登录和注册功能，支持邮箱验证
- **图书详情界面**：展示图书详细信息、用户评价、相关推荐
- **购物车界面**：显示购物车商品，支持数量修改和批量操作
- **订单界面**：订单确认、支付、查看订单状态和历史
- **用户中心界面**：个人信息管理、地址管理、订单管理、密码修改

**管理端界面：**
- **管理后台首页**：系统概览、数据统计、快捷操作
- **用户管理界面**：用户列表、用户状态管理、权限设置
- **商品管理界面**：商品列表、商品编辑、分类管理、库存管理
- **订单管理界面**：订单列表、订单处理、状态更新、数据导出
- **系统设置界面**：系统配置、参数设置、日志查看

#### 3.1.2 界面设计原则
- **一致性原则**：保持界面风格、交互方式、信息架构的一致性
- **简洁性原则**：界面简洁明了，避免冗余信息和复杂操作
- **易用性原则**：操作流程简单直观，符合用户使用习惯
- **响应性原则**：支持多设备访问，自适应不同屏幕尺寸
- **可访问性原则**：支持键盘导航，提供无障碍访问支持

### 3.2 外部接口

#### 3.2.1 第三方支付接口
- **支付宝接口**：集成支付宝开放平台API，支持网页支付和手机支付
- **微信支付接口**：集成微信支付API，支持公众号支付和扫码支付
- **银联支付接口**：集成银联在线支付API，支持银行卡支付

#### 3.2.2 物流跟踪接口
- **快递鸟API**：集成快递鸟物流跟踪API，支持多家快递公司
- **菜鸟物流API**：集成菜鸟网络物流API，提供物流信息查询
- **顺丰API**：集成顺丰速运API，支持顺丰快递跟踪

#### 3.2.3 短信邮件接口
- **阿里云短信服务**：用于发送验证码、订单通知等短信
- **腾讯云邮件服务**：用于发送邮件验证、订单确认等邮件
- **极光推送**：用于发送APP推送通知

#### 3.2.4 文件存储接口
- **阿里云OSS**：用于存储图书图片、用户头像等文件
- **腾讯云COS**：备选文件存储方案
- **七牛云存储**：备选文件存储方案

### 3.3 内部接口

#### 3.3.1 RESTful API接口规范
系统采用RESTful API设计风格，所有接口遵循统一的规范：

**接口URL规范：**
```
GET    /api/users          # 获取用户列表
GET    /api/users/{id}     # 获取指定用户信息
POST   /api/users          # 创建新用户
PUT    /api/users/{id}     # 更新用户信息
DELETE /api/users/{id}     # 删除用户

GET    /api/books          # 获取图书列表
GET    /api/books/{id}     # 获取指定图书信息
POST   /api/books          # 创建新图书
PUT    /api/books/{id}     # 更新图书信息
DELETE /api/books/{id}     # 删除图书

GET    /api/orders         # 获取订单列表
GET    /api/orders/{id}    # 获取指定订单信息
POST   /api/orders         # 创建新订单
PUT    /api/orders/{id}    # 更新订单状态
```

**请求响应格式：**
```json
// 成功响应格式
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据内容
    },
    "timestamp": "2024-01-01T12:00:00Z"
}

// 错误响应格式
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 3.3.2 核心业务接口

**用户管理接口：**
```java
// 用户注册
POST /api/user/register
{
    "account": "<EMAIL>",
    "password": "password123",
    "name": "张三"
}

// 用户登录
POST /api/user/login
{
    "account": "<EMAIL>",
    "password": "password123"
}

// 获取用户信息
GET /api/user/info
Headers: Authorization: Bearer {jwt_token}
```

**商品管理接口：**
```java
// 获取图书列表
GET /api/books?page=1&pageSize=10&category=文学&keyword=小说

// 获取图书详情
GET /api/books/{bookId}

// 添加图书（管理员）
POST /api/books
{
    "bookName": "图书名称",
    "author": "作者",
    "isbn": "*************",
    "price": 29.80,
    "stock": 100,
    "description": "图书描述"
}
```

**订单管理接口：**
```java
// 创建订单
POST /api/orders
{
    "addressId": 1,
    "bookList": [
        {
            "id": 1,
            "num": 2,
            "price": 29.80
        }
    ]
}

// 获取订单列表
GET /api/orders?page=1&pageSize=10&status=pending

// 更新订单状态
PUT /api/orders/{orderId}/status
{
    "status": "shipped",
    "logisticsCompany": "顺丰速运",
    "logisticsNum": "SF1234567890"
}
```

**购物车接口：**
```java
// 添加商品到购物车
POST /api/cart/add
{
    "bookId": 1,
    "num": 2
}

// 获取购物车列表
GET /api/cart?account=<EMAIL>

// 删除购物车商品
DELETE /api/cart/{cartId}

// 批量删除购物车商品
DELETE /api/cart/batch
{
    "ids": [1, 2, 3]
}
```

#### 3.3.3 数据传输对象(DTO)

**用户相关DTO：**
```java
public class UserDto {
    private Integer id;
    private String account;
    private String name;
    private String gender;
    private String imgUrl;
    private Boolean manage;
    private Boolean enable;
    private Date registerTime;
}

public class LoginDto {
    private String account;
    private String password;
}
```

**商品相关DTO：**
```java
public class BookDto {
    private Integer id;
    private String bookName;
    private String author;
    private String isbn;
    private String publish;
    private BigDecimal price;
    private Integer stock;
    private String description;
    private List<String> images;
}

public class BookSearchDto {
    private String keyword;
    private String category;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Integer page;
    private Integer pageSize;
}
```

**订单相关DTO：**
```java
public class OrderInitDto {
    private String account;
    private Integer addressId;
    private List<OrderBookDto> bookList;
}

public class OrderBookDto {
    private Integer id;
    private Integer num;
    private BigDecimal price;
}
```

### 3.4 用户界面设计规则

#### 3.4.1 视觉设计规范
- **色彩规范**：
  - 主色调：#409EFF (蓝色)
  - 辅助色：#67C23A (绿色)、#E6A23C (橙色)、#F56C6C (红色)
  - 中性色：#303133 (主要文字)、#606266 (常规文字)、#909399 (次要文字)
  - 背景色：#FFFFFF (主背景)、#F5F7FA (页面背景)

- **字体规范**：
  - 主字体：-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto
  - 标题字体：PingFang SC, Microsoft YaHei, sans-serif
  - 字号规范：12px (小字)、14px (正文)、16px (小标题)、18px (标题)、24px (大标题)

- **间距规范**：
  - 基础间距：4px、8px、12px、16px、20px、24px
  - 组件间距：16px (小间距)、24px (中间距)、32px (大间距)
  - 页面边距：20px (移动端)、40px (桌面端)

#### 3.4.2 交互设计规范
- **按钮设计**：
  - 主要按钮：蓝色背景，白色文字，圆角4px
  - 次要按钮：白色背景，蓝色边框，蓝色文字
  - 危险按钮：红色背景，白色文字
  - 按钮状态：正常、悬停、点击、禁用

- **表单设计**：
  - 输入框：边框1px，圆角4px，聚焦时蓝色边框
  - 标签：左对齐，必填项标红星
  - 验证：实时验证，错误信息红色显示
  - 提交：防重复提交，加载状态显示

- **反馈设计**：
  - 成功提示：绿色背景，白色文字，自动消失
  - 错误提示：红色背景，白色文字，手动关闭
  - 警告提示：橙色背景，白色文字
  - 加载状态：旋转图标，半透明遮罩

#### 3.4.3 响应式设计规范
- **断点设置**：
  - 超小屏：< 576px (手机竖屏)
  - 小屏：576px - 768px (手机横屏)
  - 中屏：768px - 992px (平板)
  - 大屏：992px - 1200px (桌面)
  - 超大屏：> 1200px (大桌面)

- **布局适配**：
  - 移动端：单列布局，触摸友好
  - 平板端：双列布局，适中间距
  - 桌面端：多列布局，充分利用空间

- **组件适配**：
  - 导航：移动端折叠菜单，桌面端水平菜单
  - 表格：移动端卡片式，桌面端表格式
  - 图片：响应式缩放，保持比例

## 4 运行设计

### 4.1 运行模块组合

#### 4.1.1 系统启动模块组合
系统启动时需要按照特定顺序加载各个模块，确保依赖关系正确：

**启动顺序：**
1. **配置加载模块**：加载application.yml配置文件，初始化系统参数
2. **数据库连接模块**：建立MySQL数据库连接池，验证数据库连接
3. **安全模块**：初始化Spring Security配置，加载JWT密钥
4. **缓存模块**：初始化Redis连接，配置缓存策略
5. **业务模块**：按依赖顺序加载Service和Controller
6. **Web模块**：启动内嵌Tomcat服务器，注册路由

**模块依赖关系：**
```
配置模块 → 数据库模块 → 安全模块 → 缓存模块 → 业务模块 → Web模块
    ↓         ↓         ↓         ↓         ↓         ↓
  参数配置   连接池    JWT配置   Redis配置  业务逻辑   HTTP服务
```

#### 4.1.2 运行时模块协作
系统运行时各模块之间的协作关系：

**请求处理流程：**
```
HTTP请求 → 安全过滤器 → 控制器 → 业务服务 → 数据访问 → 数据库
    ↓         ↓         ↓         ↓         ↓         ↓
  路由解析   权限验证   参数验证   业务逻辑   SQL执行   数据返回
```

**模块间通信：**
- **同步调用**：Controller调用Service，Service调用Mapper
- **异步处理**：订单处理、邮件发送、日志记录
- **事件驱动**：用户注册事件、订单状态变更事件
- **缓存机制**：热点数据缓存，减少数据库访问

### 4.2 运行控制

#### 4.2.1 系统监控控制
- **健康检查**：定期检查数据库连接、Redis连接、外部API可用性
- **性能监控**：监控CPU使用率、内存使用率、响应时间、吞吐量
- **错误监控**：捕获异常信息，记录错误日志，发送告警通知
- **资源监控**：监控磁盘空间、网络带宽、数据库连接数

#### 4.2.2 流量控制
- **限流控制**：基于IP和用户的请求频率限制
- **熔断机制**：当下游服务不可用时，快速失败避免雪崩
- **负载均衡**：多实例部署时的请求分发策略
- **缓存控制**：热点数据缓存，减轻数据库压力

#### 4.2.3 事务控制
- **数据库事务**：确保数据操作的ACID特性
- **分布式事务**：跨服务的事务一致性保障
- **补偿机制**：事务失败时的数据回滚和补偿
- **幂等性控制**：防止重复操作导致的数据不一致

### 4.3 运行时间

#### 4.3.1 系统启动时间
- **冷启动时间**：首次启动约30-60秒
- **热启动时间**：重启约15-30秒
- **模块加载时间**：各模块加载时间分布
- **优化目标**：启动时间控制在30秒以内

#### 4.3.2 响应时间要求
- **页面加载时间**：首页加载时间 < 2秒
- **API响应时间**：
  - 查询接口：< 500ms
  - 更新接口：< 1000ms
  - 复杂查询：< 2000ms
- **文件上传时间**：图片上传 < 5秒
- **数据库查询时间**：单表查询 < 100ms，关联查询 < 500ms

#### 4.3.3 批处理时间
- **数据备份**：每日凌晨2:00执行，耗时约30分钟
- **日志清理**：每周日凌晨3:00执行，耗时约10分钟
- **统计报表**：每日凌晨4:00生成，耗时约15分钟
- **缓存预热**：每日凌晨5:00执行，耗时约5分钟

## 5 系统数据结构设计

### 5.1 逻辑结构设计要点

#### 5.1.1 实体关系设计
系统采用关系型数据库设计，主要实体及其关系如下：

**核心实体关系：**
```
用户(User) 1:N 地址(Address)
用户(User) 1:N 购物车(Cart)
用户(User) 1:N 订单(Order)

图书(Book) 1:N 图书图片(BookImg)
图书(Book) N:N 图书分类(BookSort) [通过BookSortList关联]
图书(Book) N:1 出版社(Publish)

订单(Order) 1:N 订单详情(OrderDetail)
订单(Order) 1:1 订单费用(Expense)
订单(Order) N:1 地址(Address)

购物车(Cart) N:1 用户(User)
购物车(Cart) N:1 图书(Book)

书单(BookTopic) 1:N 书单图书(SubBookTopic)
书单图书(SubBookTopic) N:1 图书(Book)
```

**实体属性设计：**
- **用户实体**：包含基本信息、权限信息、状态信息
- **图书实体**：包含基本信息、价格信息、库存信息、状态信息
- **订单实体**：包含订单基本信息、状态信息、时间信息
- **地址实体**：包含收货人信息、地址信息、标签信息

#### 5.1.2 数据完整性约束
- **主键约束**：每个表都有唯一主键，采用自增整数
- **外键约束**：维护表间引用完整性，防止孤立数据
- **唯一约束**：用户账号、图书ISBN等字段保证唯一性
- **非空约束**：关键字段不允许为空值
- **检查约束**：价格、库存等数值字段的范围检查

#### 5.1.3 索引设计策略
- **主键索引**：自动创建，用于快速定位记录
- **唯一索引**：用户账号、图书ISBN等唯一字段
- **普通索引**：常用查询字段，如图书名称、作者
- **复合索引**：多字段组合查询，如用户+图书的购物车查询
- **全文索引**：图书描述等文本字段的全文搜索

### 5.2 物理结构设计要点

#### 5.2.1 数据库表结构设计

**用户相关表：**
```sql
-- 用户表
CREATE TABLE `user` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户编号',
    `account` varchar(100) NOT NULL COMMENT '用户账号(邮箱)',
    `password` varchar(255) NOT NULL COMMENT '密码',
    `name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
    `gender` varchar(10) DEFAULT NULL COMMENT '性别',
    `imgUrl` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `info` text COMMENT '个人简介',
    `manage` tinyint(1) DEFAULT '0' COMMENT '是否为管理员',
    `enable` tinyint(1) DEFAULT '1' COMMENT '是否启用',
    `registerTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_account` (`account`),
    KEY `idx_register_time` (`registerTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 地址表
CREATE TABLE `address` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地址编号',
    `account` varchar(100) NOT NULL COMMENT '用户账号',
    `name` varchar(50) NOT NULL COMMENT '收货人姓名',
    `phone` varchar(20) NOT NULL COMMENT '收货人电话',
    `addr` varchar(500) NOT NULL COMMENT '具体地址',
    `label` varchar(20) DEFAULT NULL COMMENT '地址标签',
    `off` tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_account` (`account`),
    FOREIGN KEY (`account`) REFERENCES `user` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地址表';
```

**商品相关表：**
```sql
-- 图书表
CREATE TABLE `book` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '图书编号',
    `bookName` varchar(255) NOT NULL COMMENT '图书名称',
    `author` varchar(255) DEFAULT NULL COMMENT '作者',
    `isbn` varchar(50) NOT NULL COMMENT 'ISBN号',
    `publish` varchar(255) DEFAULT NULL COMMENT '出版社',
    `birthday` timestamp NULL DEFAULT NULL COMMENT '出版时间',
    `marketPrice` decimal(10,2) DEFAULT NULL COMMENT '市场价',
    `price` decimal(10,2) DEFAULT NULL COMMENT '售价',
    `stock` int(11) DEFAULT '0' COMMENT '库存',
    `description` text COMMENT '图书描述',
    `put` tinyint(1) DEFAULT '1' COMMENT '是否上架',
    `rank` int(11) DEFAULT '0' COMMENT '权重值',
    `newProduct` tinyint(1) DEFAULT '0' COMMENT '是否新品',
    `recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_isbn` (`isbn`),
    KEY `idx_book_name` (`bookName`),
    KEY `idx_author` (`author`),
    KEY `idx_price` (`price`),
    KEY `idx_stock` (`stock`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书表';

-- 图书分类表
CREATE TABLE `booksort` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
    `sortName` varchar(100) NOT NULL COMMENT '分类名称',
    `parentId` int(11) DEFAULT '0' COMMENT '父分类ID',
    `level` int(11) DEFAULT '1' COMMENT '分类层级',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parentId`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类表';
```

**订单相关表：**
```sql
-- 订单表
CREATE TABLE `bookorder` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单编号',
    `orderId` varchar(50) NOT NULL COMMENT '订单号',
    `account` varchar(100) NOT NULL COMMENT '用户账号',
    `addressId` int(11) NOT NULL COMMENT '地址编号',
    `orderTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    `shipTime` timestamp NULL DEFAULT NULL COMMENT '发货时间',
    `getTime` timestamp NULL DEFAULT NULL COMMENT '收货时间',
    `evaluateTime` timestamp NULL DEFAULT NULL COMMENT '评价时间',
    `closeTime` timestamp NULL DEFAULT NULL COMMENT '关闭时间',
    `confirmTime` timestamp NULL DEFAULT NULL COMMENT '确认时间',
    `orderStatus` varchar(20) DEFAULT 'pending' COMMENT '订单状态',
    `logisticsCompany` varchar(100) DEFAULT NULL COMMENT '物流公司',
    `logisticsNum` varchar(100) DEFAULT NULL COMMENT '物流单号',
    `beUserDelete` tinyint(1) DEFAULT '0' COMMENT '用户删除标记',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`orderId`),
    KEY `idx_account` (`account`),
    KEY `idx_order_time` (`orderTime`),
    KEY `idx_order_status` (`orderStatus`),
    FOREIGN KEY (`account`) REFERENCES `user` (`account`),
    FOREIGN KEY (`addressId`) REFERENCES `address` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单详情表
CREATE TABLE `orderdetail` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '详情编号',
    `orderId` varchar(50) NOT NULL COMMENT '订单号',
    `bookId` int(11) NOT NULL COMMENT '图书编号',
    `num` int(11) NOT NULL COMMENT '购买数量',
    `price` decimal(10,2) NOT NULL COMMENT '购买价格',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`orderId`),
    KEY `idx_book_id` (`bookId`),
    FOREIGN KEY (`orderId`) REFERENCES `bookorder` (`orderId`),
    FOREIGN KEY (`bookId`) REFERENCES `book` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单详情表';
```

#### 5.2.2 数据存储优化
- **分区策略**：按时间分区存储历史订单数据
- **索引优化**：根据查询模式创建合适的索引
- **数据压缩**：对历史数据进行压缩存储
- **读写分离**：主库写入，从库查询，提高并发性能

#### 5.2.3 数据备份策略
- **全量备份**：每周进行一次完整数据备份
- **增量备份**：每日进行增量数据备份
- **实时备份**：关键数据实时同步到备库
- **异地备份**：定期将备份数据传输到异地存储

### 5.3 数据结构与程序的关系

#### 5.3.1 ORM映射关系
系统使用MyBatis作为ORM框架，实现对象与数据库表的映射：

**实体类映射：**
```java
// 用户实体类
@Data
public class User {
    private Integer id;
    private String account;
    private String password;
    private String name;
    private String gender;
    private String imgUrl;
    private String info;
    private Boolean manage;
    private Boolean enable;
    private Date registerTime;
}

// 图书实体类
@Data
public class Book {
    private Integer id;
    private String bookName;
    private String author;
    private String isbn;
    private String publish;
    private Date birthday;
    private BigDecimal marketPrice;
    private BigDecimal price;
    private Integer stock;
    private String description;
    private Boolean put;
    private Integer rank;
    private Boolean newProduct;
    private Boolean recommend;
}
```

**Mapper接口映射：**
```java
@Repository
public interface UserMapper {
    int insertUser(User user);
    User selectUserByAccount(String account);
    int updateUser(User user);
    int deleteUser(Integer id);
    List<User> selectUsersByPage(@Param("offset") int offset, @Param("limit") int limit);
}

@Repository
public interface BookMapper {
    int insertBook(Book book);
    Book selectBookById(Integer id);
    int updateBook(Book book);
    int deleteBook(Integer id);
    List<Book> selectBooksByCondition(BookSearchDto searchDto);
}
```

#### 5.3.2 数据访问层设计
- **DAO模式**：数据访问对象模式，封装数据库操作
- **连接池管理**：使用Druid连接池管理数据库连接
- **事务管理**：使用Spring事务管理器控制事务
- **SQL优化**：通过索引和查询优化提高性能

#### 5.3.3 缓存机制设计
- **一级缓存**：MyBatis Session级别缓存
- **二级缓存**：MyBatis Mapper级别缓存
- **Redis缓存**：分布式缓存，存储热点数据
- **缓存策略**：LRU淘汰策略，定时刷新机制

## 6 系统出错设计

### 6.1 出错信息

#### 6.1.1 错误分类体系
系统采用分层的错误分类体系，便于错误定位和处理：

**按错误级别分类：**
- **FATAL（致命错误）**：系统无法继续运行的错误
- **ERROR（错误）**：功能无法正常执行的错误
- **WARN（警告）**：可能影响系统性能或功能的问题
- **INFO（信息）**：系统运行的重要信息
- **DEBUG（调试）**：开发调试信息

**按错误来源分类：**
- **系统错误**：数据库连接失败、内存溢出、网络异常
- **业务错误**：参数验证失败、业务规则违反、权限不足
- **用户错误**：输入格式错误、操作超时、重复提交
- **外部错误**：第三方服务异常、网络超时、API调用失败

#### 6.1.2 错误码设计规范
采用5位数字错误码，便于错误分类和定位：

**错误码结构：**
```
XYZAB
X: 错误级别 (1-致命, 2-错误, 3-警告, 4-信息)
Y: 模块编号 (0-系统, 1-用户, 2-商品, 3-订单, 4-购物车)
Z: 功能编号 (0-通用, 1-查询, 2-新增, 3-修改, 4-删除)
AB: 具体错误序号 (01-99)
```

**常用错误码定义：**
```java
public class ErrorCode {
    // 系统级错误 (10xxx)
    public static final int SYSTEM_ERROR = 10001;
    public static final int DATABASE_ERROR = 10002;
    public static final int NETWORK_ERROR = 10003;
    public static final int CACHE_ERROR = 10004;

    // 用户模块错误 (21xxx)
    public static final int USER_NOT_FOUND = 21101;
    public static final int USER_ALREADY_EXISTS = 21201;
    public static final int INVALID_PASSWORD = 21102;
    public static final int ACCOUNT_DISABLED = 21103;

    // 商品模块错误 (22xxx)
    public static final int BOOK_NOT_FOUND = 22101;
    public static final int INSUFFICIENT_STOCK = 22102;
    public static final int BOOK_ALREADY_EXISTS = 22201;

    // 订单模块错误 (23xxx)
    public static final int ORDER_NOT_FOUND = 23101;
    public static final int ORDER_STATUS_ERROR = 23102;
    public static final int PAYMENT_FAILED = 23103;
}
```

#### 6.1.3 错误信息格式
统一的错误响应格式，便于前端处理：

```json
{
    "success": false,
    "code": 21101,
    "message": "用户不存在",
    "data": null,
    "timestamp": "2024-01-01T12:00:00Z",
    "path": "/api/user/login",
    "error": {
        "type": "BusinessException",
        "details": "用户账号 <EMAIL> 不存在",
        "suggestion": "请检查账号是否正确或进行注册"
    }
}
```

### 6.2 补救措施

#### 6.2.1 自动恢复机制
- **重试机制**：网络请求失败时自动重试，最多重试3次
- **熔断机制**：服务异常时快速失败，避免级联故障
- **降级机制**：核心服务不可用时，提供基础功能
- **故障转移**：主服务异常时，自动切换到备用服务

#### 6.2.2 数据恢复策略
- **事务回滚**：数据操作失败时自动回滚事务
- **数据补偿**：分布式事务失败时的数据补偿机制
- **备份恢复**：系统故障时从备份数据恢复
- **增量恢复**：基于日志的增量数据恢复

#### 6.2.3 用户体验保障
- **友好提示**：将技术错误转换为用户友好的提示信息
- **操作指导**：提供解决问题的操作建议
- **客服支持**：提供客服联系方式，人工协助解决
- **状态保存**：操作失败时保存用户输入状态

#### 6.2.4 系统监控告警
- **实时监控**：监控系统关键指标，及时发现异常
- **告警通知**：异常发生时立即通知运维人员
- **日志分析**：分析错误日志，定位问题根因
- **性能分析**：分析系统性能，预防潜在问题

### 6.3 系统维护设计

#### 6.3.1 日志管理策略
- **日志分级**：按照不同级别记录日志信息
- **日志轮转**：定期轮转日志文件，避免文件过大
- **日志压缩**：压缩历史日志文件，节省存储空间
- **日志清理**：定期清理过期日志文件

**日志配置示例：**
```xml
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/bookstore.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/bookstore.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.huang.store" level="INFO"/>
    <root level="WARN">
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

#### 6.3.2 性能监控体系
- **应用监控**：监控应用性能指标，如响应时间、吞吐量
- **系统监控**：监控服务器资源使用情况
- **数据库监控**：监控数据库性能和连接状态
- **业务监控**：监控关键业务指标，如订单量、用户活跃度

#### 6.3.3 维护工具设计
- **健康检查接口**：提供系统健康状态检查接口
- **配置管理工具**：动态修改系统配置参数
- **数据库管理工具**：数据库备份、恢复、优化工具
- **日志分析工具**：日志查询、分析、统计工具

#### 6.3.4 升级维护策略
- **版本管理**：采用语义化版本号管理系统版本
- **灰度发布**：新版本逐步发布，降低升级风险
- **回滚机制**：升级失败时快速回滚到稳定版本
- **数据迁移**：数据库结构变更时的数据迁移策略

---

## 总结

本书店管理系统项目概要设计说明书详细描述了系统的整体架构、技术选型、模块设计和实现方案。系统采用现代化的技术栈，具有良好的可扩展性和可维护性，能够满足中小型书店的数字化运营需求。

系统的主要特点包括：
1. **技术先进**：采用Spring Boot + Vue.js + MySQL的现代技术栈
2. **架构清晰**：前后端分离，模块化设计，职责明确
3. **功能完整**：涵盖电商平台的核心功能模块
4. **安全可靠**：完善的安全机制和错误处理机制
5. **易于维护**：规范的代码结构和完善的文档

通过本设计说明书，开发团队可以清晰地理解系统的整体设计思路，为后续的详细设计和开发实现提供重要指导。
