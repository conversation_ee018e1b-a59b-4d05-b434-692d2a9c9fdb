-- ========================================
-- 书店系统完整示例数据插入脚本
-- 注意：请先执行 all.sql 创建表结构
-- 执行顺序：1. all.sql  2. sample_data.sql
-- ========================================

USE `bookstore`;

-- ========================================
-- 用户数据
-- ========================================

-- 插入管理员用户和测试用户
INSERT INTO `user` (`account`, `password`, `name`, `gender`, `manage`, `enable`) VALUES
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', '男', 1, 1),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '张三', '男', 0, 1),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '李四', '女', 0, 1),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '王五', '男', 0, 1),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '赵六', '女', 0, 1);

-- 插入地址数据
INSERT INTO `address` (`account`, `name`, `phone`, `addr`, `label`) VALUES
('<EMAIL>', '张三', '***********', '北京市朝阳区某某街道123号', '家'),
('<EMAIL>', '张三', '***********', '北京市海淀区某某大厦456号', '公司'),
('<EMAIL>', '李四', '***********', '上海市浦东新区某某路789号', '家'),
('<EMAIL>', '王五', '***********', '广州市天河区某某小区101号', '家'),
('<EMAIL>', '赵六', '***********', '深圳市南山区某某写字楼202号', '公司');

-- ========================================
-- 图书分类数据
-- ========================================

-- 一级分类
INSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES
('文学', '无', '级别一', 1),
('科技', '无', '级别一', 2),
('教育', '无', '级别一', 3),
('生活', '无', '级别一', 4),
('艺术', '无', '级别一', 5);

-- 二级分类
INSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES
('小说', '文学', '级别二', 1),
('散文', '文学', '级别二', 2),
('诗歌', '文学', '级别二', 3),
('计算机', '科技', '级别二', 1),
('电子', '科技', '级别二', 2),
('教材', '教育', '级别二', 1),
('考试', '教育', '级别二', 2),
('健康', '生活', '级别二', 1),
('美食', '生活', '级别二', 2),
('绘画', '艺术', '级别二', 1),
('音乐', '艺术', '级别二', 2);

-- ========================================
-- 出版社数据
-- ========================================

INSERT INTO `publish` (`name`, `showPublish`, `rank`) VALUES
('人民文学出版社', 1, 1),
('机械工业出版社', 1, 2),
('清华大学出版社', 1, 3),
('电子工业出版社', 1, 4),
('中信出版社', 1, 5),
('商务印书馆', 1, 6),
('北京大学出版社', 1, 7),
('上海译文出版社', 1, 8);

-- ========================================
-- 图书数据
-- ========================================

INSERT INTO `book` (`bookName`, `author`, `isbn`, `publish`, `birthday`, `marketPrice`, `price`, `stock`, `description`, `put`, `rank`, `newProduct`, `recommend`) VALUES
('红楼梦', '曹雪芹', '9787020002207', '人民文学出版社', '2020-01-01 00:00:00', 59.00, 45.00, 100, '中国古典文学四大名著之一，描绘了贾宝玉、林黛玉等人的爱情悲剧', 1, 10, 0, 1),
('Java核心技术', 'Cay S. Horstmann', '9787111213826', '机械工业出版社', '2021-03-15 00:00:00', 128.00, 98.00, 50, 'Java编程经典教程，全面介绍Java语言特性和编程技巧', 1, 9, 1, 1),
('算法导论', 'Thomas H. Cormen', '9787111407010', '机械工业出版社', '2020-06-01 00:00:00', 158.00, 128.00, 30, '计算机算法经典教材，深入讲解各种算法设计与分析', 1, 8, 0, 1),
('Spring Boot实战', '汪云飞', '9787121291005', '电子工业出版社', '2021-05-20 00:00:00', 89.00, 69.00, 80, 'Spring Boot开发实战指南，快速构建企业级应用', 1, 7, 1, 0),
('西游记', '吴承恩', '9787020002214', '人民文学出版社', '2020-02-01 00:00:00', 49.00, 38.00, 120, '中国古典文学四大名著之一，讲述孙悟空等人西天取经的故事', 1, 6, 0, 1),
('三国演义', '罗贯中', '9787020002221', '人民文学出版社', '2020-03-01 00:00:00', 55.00, 42.00, 90, '中国古典文学四大名著之一，描写三国时期的历史风云', 1, 5, 0, 1),
('水浒传', '施耐庵', '9787020002238', '人民文学出版社', '2020-04-01 00:00:00', 52.00, 39.00, 85, '中国古典文学四大名著之一，讲述梁山好汉的英雄故事', 1, 4, 0, 1),
('深入理解计算机系统', 'Randal E. Bryant', '9787111544937', '机械工业出版社', '2021-01-01 00:00:00', 139.00, 108.00, 40, '计算机系统经典教材，深入理解计算机工作原理', 1, 3, 1, 1),
('设计模式', 'Erich Gamma', '9787111075776', '机械工业出版社', '2020-08-01 00:00:00', 69.00, 52.00, 60, '软件设计模式经典著作，提高代码质量和可维护性', 1, 2, 0, 0),
('人工智能：一种现代的方法', 'Stuart Russell', '9787302511946', '清华大学出版社', '2021-09-01 00:00:00', 168.00, 135.00, 25, '人工智能领域权威教材，全面介绍AI理论与实践', 1, 1, 1, 1);

-- ========================================
-- 图书图片数据
-- ========================================

INSERT INTO `bookimg` (`isbn`, `imgSrc`, `cover`) VALUES
('9787020002207', 'static/image/book/hongloumeng_cover.jpg', 1),
('9787020002207', 'static/image/book/hongloumeng_1.jpg', 0),
('9787111213826', 'static/image/book/java_cover.jpg', 1),
('9787111213826', 'static/image/book/java_1.jpg', 0),
('9787111407010', 'static/image/book/algorithm_cover.jpg', 1),
('9787121291005', 'static/image/book/springboot_cover.jpg', 1),
('9787020002214', 'static/image/book/xiyouji_cover.jpg', 1),
('9787020002221', 'static/image/book/sanguo_cover.jpg', 1),
('9787020002238', 'static/image/book/shuihu_cover.jpg', 1),
('9787111544937', 'static/image/book/csapp_cover.jpg', 1),
('9787111075776', 'static/image/book/design_pattern_cover.jpg', 1),
('9787302511946', 'static/image/book/ai_cover.jpg', 1);

-- ========================================
-- 图书分类关联数据
-- ========================================

INSERT INTO `booksortlist` (`bookSortId`, `bookId`) VALUES
(6, 1),   -- 红楼梦 -> 小说
(6, 5),   -- 西游记 -> 小说
(6, 6),   -- 三国演义 -> 小说
(6, 7),   -- 水浒传 -> 小说
(9, 2),   -- Java核心技术 -> 计算机
(9, 3),   -- 算法导论 -> 计算机
(9, 4),   -- Spring Boot实战 -> 计算机
(9, 8),   -- 深入理解计算机系统 -> 计算机
(9, 9),   -- 设计模式 -> 计算机
(9, 10);  -- 人工智能 -> 计算机

-- ========================================
-- 书单数据
-- ========================================

INSERT INTO `topic` (`title`,`subTitle`,`cover`,`rank`,`status`,`viewCnt`,`favCnt`,`orderCnt`,`createdAt`,`updatedAt`) VALUES
('程序员必读书单','提升编程技能的经典书籍','static/image/topic/programmer_books.jpg',1,1,156,23,5,NOW(),NOW()),
('古典文学精选','传承千年的文学瑰宝','static/image/topic/classic_literature.jpg',2,1,289,45,12,NOW(),NOW()),
('人工智能入门','AI时代必备知识','static/image/topic/ai_books.jpg',3,1,98,15,3,NOW(),NOW());

-- 书单条目
INSERT INTO `topic_item` (`topicId`,`bookId`,`recommendReason`,`orderNo`) VALUES
(1,2,'Java开发者的必备参考书，内容全面深入，适合各个层次的开发者',1),
(1,3,'算法学习的经典教材，计算机科学基础，提升编程思维',2),
(1,4,'现代Java开发框架实战指南，快速上手企业级开发',3),
(1,8,'深入理解计算机底层原理，成为优秀程序员的必经之路',4),
(1,9,'软件设计经典，学会写出优雅可维护的代码',5),
(2,1,'中国古典小说的巅峰之作，文学价值极高，人物刻画入木三分',1),
(2,5,'神话色彩浓厚的古典小说，想象力丰富，寓意深刻',2),
(2,6,'历史演义小说的典范，智谋与英雄并存',3),
(2,7,'英雄传奇的经典之作，义薄云天的好汉故事',4),
(3,10,'AI领域权威教材，理论与实践并重，适合入门学习',1);

-- 示例收藏
INSERT INTO `topic_fav` (`userAccount`,`topicId`,`favAt`) VALUES
('<EMAIL>',1,NOW()),
('<EMAIL>',3,NOW()),
('<EMAIL>',2,NOW()),
('<EMAIL>',1,NOW()),
('<EMAIL>',2,NOW());

-- ========================================
-- 购物车示例数据
-- ========================================

INSERT INTO `cart` (`account`, `id`, `num`, `addTime`) VALUES
('<EMAIL>', 1, 2, NOW()),
('<EMAIL>', 2, 1, NOW()),
('<EMAIL>', 3, 1, NOW()),
('<EMAIL>', 4, 3, NOW()),
('<EMAIL>', 5, 1, NOW());

-- ========================================
-- 订单示例数据
-- ========================================

INSERT INTO `bookorder` (`orderId`, `account`, `addressId`, `orderTime`, `orderStatus`) VALUES
('ORD202401010001', '<EMAIL>', 1, '2024-01-01 10:30:00', '已完成'),
('ORD202401020001', '<EMAIL>', 3, '2024-01-02 14:20:00', '待发货'),
('ORD202401030001', '<EMAIL>', 4, '2024-01-03 16:45:00', '已完成'),
('ORD202401040001', '<EMAIL>', 5, '2024-01-04 09:15:00', '待支付');

INSERT INTO `orderdetail` (`orderId`, `bookId`, `num`, `price`) VALUES
('ORD202401010001', 1, 1, 45.00),
('ORD202401010001', 2, 1, 98.00),
('ORD202401020001', 3, 1, 128.00),
('ORD202401030001', 4, 2, 69.00),
('ORD202401030001', 5, 1, 38.00),
('ORD202401040001', 6, 1, 42.00);

INSERT INTO `expense` (`orderId`, `productTotalMoney`, `freight`, `coupon`, `activityDiscount`, `allPrice`, `finallyPrice`) VALUES
('ORD202401010001', 143.00, 0.00, 0, 0.00, 143.00, 143.00),
('ORD202401020001', 128.00, 0.00, 0, 0.00, 128.00, 128.00),
('ORD202401030001', 176.00, 0.00, 0, 0.00, 176.00, 176.00),
('ORD202401040001', 42.00, 0.00, 0, 0.00, 42.00, 42.00);

-- ========================================
-- 图书评论数据
-- ========================================

-- 一级评论（直接对图书的评论）
INSERT INTO `book_comment` (`bookId`, `userId`, `content`, `parentId`, `createTime`) VALUES
(1, 2, '红楼梦真是经典之作，人物刻画细腻，情节跌宕起伏，值得反复品读。', NULL, '2024-01-01 15:30:00'),
(1, 3, '曹雪芹的文笔太棒了，每个人物都有自己的特色，林黛玉的形象特别深刻。', NULL, '2024-01-02 10:20:00'),
(2, 4, 'Java核心技术这本书内容很全面，适合Java开发者深入学习，推荐！', NULL, '2024-01-03 14:15:00'),
(2, 5, '作为Java程序员，这本书确实是必读的，例子很实用。', NULL, '2024-01-04 09:45:00'),
(3, 2, '算法导论难度较高，但是内容很权威，适合有一定基础的读者。', NULL, '2024-01-05 16:30:00'),
(4, 3, 'Spring Boot实战很实用，跟着书中的例子可以快速上手。', NULL, '2024-01-06 11:20:00'),
(5, 4, '西游记百读不厌，孙悟空的形象太经典了！', NULL, '2024-01-07 13:10:00'),
(6, 5, '三国演义中的智谋故事很精彩，诸葛亮真是智慧的化身。', NULL, '2024-01-08 15:45:00');

-- 二级评论（对一级评论的回复）
INSERT INTO `book_comment` (`bookId`, `userId`, `content`, `parentId`, `createTime`) VALUES
(1, 4, '同意楼上的观点，红楼梦的文学价值确实很高。', 1, '2024-01-01 16:00:00'),
(1, 5, '我也很喜欢林黛玉这个角色，很有个性。', 2, '2024-01-02 11:30:00'),
(2, 2, '确实，这本书的例子都很经典，学到了很多。', 3, '2024-01-03 15:20:00'),
(2, 3, '我也在用这本书学习Java，受益匪浅。', 4, '2024-01-04 10:30:00'),
(5, 2, '孙悟空七十二变太厉害了，小时候的偶像！', 7, '2024-01-07 14:00:00');

-- ========================================
-- 评论点赞数据（需要先查看all.sql中是否有comment_like表）
-- 注意：如果all.sql中没有comment_like表，请删除此部分
-- ========================================

-- 暂时注释掉，因为需要确认all.sql中是否有comment_like表
-- INSERT INTO `comment_like` (`commentId`, `userId`, `createTime`) VALUES
-- (1, 3, '2024-01-01 16:30:00'),
-- (1, 4, '2024-01-01 17:00:00'),
-- (1, 5, '2024-01-01 18:15:00'),
-- (2, 2, '2024-01-02 12:00:00'),
-- (2, 4, '2024-01-02 13:30:00'),
-- (3, 2, '2024-01-03 15:45:00'),
-- (3, 3, '2024-01-03 16:20:00'),
-- (4, 2, '2024-01-04 11:00:00'),
-- (4, 4, '2024-01-04 12:15:00'),
-- (7, 2, '2024-01-07 14:30:00'),
-- (7, 3, '2024-01-07 15:00:00'),
-- (7, 5, '2024-01-07 16:45:00');

-- ========================================
-- 优惠券模板数据
-- ========================================

INSERT INTO `coupon_template` (`name`, `type`, `discount_value`, `min_order_amount`, `max_discount_amount`, `total_quantity`, `per_user_limit`, `valid_days`, `status`, `create_time`, `update_time`) VALUES
('新用户专享券', 1, 20.00, 100.00, NULL, 1000, 1, 30, 1, NOW(), NOW()),
('满减优惠券', 1, 50.00, 200.00, NULL, 500, 2, 60, 1, NOW(), NOW()),
('图书专享折扣', 2, 85.00, 50.00, 20.00, 800, 3, 90, 1, NOW(), NOW()),
('限时特惠券', 1, 30.00, 150.00, NULL, 200, 1, 15, 1, NOW(), NOW()),
('会员专属折扣', 2, 80.00, 80.00, 30.00, 300, 2, 180, 1, NOW(), NOW());

-- ========================================
-- 用户优惠券数据
-- ========================================

INSERT INTO `user_coupon` (`coupon_template_id`, `account`, `coupon_code`, `status`, `receive_time`, `use_time`, `order_id`, `expire_time`, `discount_amount`, `create_time`, `update_time`) VALUES
(1, '<EMAIL>', 'NEW20240101001', 2, '2024-01-01 09:00:00', '2024-01-01 10:30:00', 'ORD202401010001', '2024-01-31 23:59:59', 20.00, '2024-01-01 09:00:00', '2024-01-01 10:30:00'),
(2, '<EMAIL>', 'FULL50240101001', 1, '2024-01-01 09:00:00', NULL, NULL, '2024-03-01 23:59:59', 0.00, '2024-01-01 09:00:00', '2024-01-01 09:00:00'),
(3, '<EMAIL>', 'DISC15240101001', 1, '2024-01-01 09:00:00', NULL, NULL, '2024-04-01 23:59:59', 0.00, '2024-01-01 09:00:00', '2024-01-01 09:00:00'),
(1, '<EMAIL>', 'NEW20240102001', 1, '2024-01-02 08:30:00', NULL, NULL, '2024-02-01 23:59:59', 0.00, '2024-01-02 08:30:00', '2024-01-02 08:30:00'),
(3, '<EMAIL>', 'DISC15240102001', 1, '2024-01-02 08:30:00', NULL, NULL, '2024-04-02 23:59:59', 0.00, '2024-01-02 08:30:00', '2024-01-02 08:30:00'),
(1, '<EMAIL>', 'NEW20240103001', 2, '2024-01-03 08:00:00', '2024-01-03 16:45:00', 'ORD202401030001', '2024-02-02 23:59:59', 20.00, '2024-01-03 08:00:00', '2024-01-03 16:45:00'),
(4, '<EMAIL>', 'LIMIT30240103001', 1, '2024-01-03 08:00:00', NULL, NULL, '2024-01-18 23:59:59', 0.00, '2024-01-03 08:00:00', '2024-01-03 08:00:00'),
(2, '<EMAIL>', 'FULL50240104001', 1, '2024-01-04 07:45:00', NULL, NULL, '2024-03-04 23:59:59', 0.00, '2024-01-04 07:45:00', '2024-01-04 07:45:00'),
(5, '<EMAIL>', 'VIP20240104001', 1, '2024-01-04 07:45:00', NULL, NULL, '2024-07-02 23:59:59', 0.00, '2024-01-04 07:45:00', '2024-01-04 07:45:00');

-- ========================================
-- 秒杀活动数据
-- ========================================

INSERT INTO `spikeactivity` (`activityName`, `activityDesc`, `startTime`, `endTime`, `status`, `createTime`, `updateTime`, `createdBy`) VALUES
('新年特惠秒杀', '新年期间限时秒杀活动，精选图书超低价抢购', '2024-01-01 10:00:00', '2024-01-01 12:00:00', 2, NOW(), NOW(), '<EMAIL>'),
('午间秒杀场', '午间休息时间秒杀专场，白领充电好时机', '2024-01-15 12:00:00', '2024-01-15 14:00:00', 1, NOW(), NOW(), '<EMAIL>'),
('晚间秒杀场', '晚间黄金时段秒杀活动，下班后的阅读时光', '2024-01-20 20:00:00', '2024-01-20 22:00:00', 0, NOW(), NOW(), '<EMAIL>'),
('周末特价', '周末放松时光，好书特价来袭', '2024-01-27 09:00:00', '2024-01-28 21:00:00', 0, NOW(), NOW(), '<EMAIL>');

-- 秒杀商品数据
INSERT INTO `spikegoods` (`activityId`, `bookId`, `spikePrice`, `originalPrice`, `spikeStock`, `soldCount`, `limitPerUser`, `sortOrder`, `status`, `createTime`, `updateTime`) VALUES
(1, 1, 19.90, 45.00, 50, 45, 2, 1, 1, NOW(), NOW()),  -- 红楼梦
(1, 2, 49.90, 98.00, 30, 28, 1, 2, 1, NOW(), NOW()),  -- Java核心技术
(1, 3, 69.90, 128.00, 20, 18, 1, 3, 1, NOW(), NOW()), -- 算法导论
(2, 4, 39.90, 69.00, 40, 15, 2, 1, 1, NOW(), NOW()),  -- Spring Boot实战
(2, 5, 15.90, 38.00, 60, 25, 2, 2, 1, NOW(), NOW()),  -- 西游记
(2, 6, 22.90, 42.00, 35, 12, 1, 3, 1, NOW(), NOW()),  -- 三国演义
(3, 1, 22.90, 45.00, 30, 0, 1, 1, 1, NOW(), NOW()),   -- 红楼梦（晚场）
(3, 2, 59.90, 98.00, 25, 0, 1, 2, 1, NOW(), NOW()),   -- Java核心技术（晚场）
(4, 7, 19.90, 39.00, 80, 0, 3, 1, 1, NOW(), NOW()),   -- 水浒传
(4, 8, 79.90, 108.00, 20, 0, 1, 2, 1, NOW(), NOW());  -- 深入理解计算机系统

-- 秒杀记录数据
INSERT INTO `spikerecord` (`spikeGoodsId`, `userAccount`, `quantity`, `spikeTime`, `result`, `failReason`, `ipAddress`, `userAgent`) VALUES
(1, '<EMAIL>', 2, '2024-01-01 10:00:05', 1, NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, '<EMAIL>', 1, '2024-01-01 10:00:08', 1, NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, '<EMAIL>', 1, '2024-01-01 10:05:12', 1, NULL, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
(2, '<EMAIL>', 1, '2024-01-01 10:05:15', 1, NULL, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
(4, '<EMAIL>', 2, '2024-01-15 12:00:03', 1, NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(5, '<EMAIL>', 1, '2024-01-15 12:01:20', 1, NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, '<EMAIL>', 1, '2024-01-01 11:30:45', 0, '库存不足', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'),
(2, '<EMAIL>', 1, '2024-01-01 11:45:30', 0, '超出限购数量', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

-- ========================================
-- 推荐图书数据
-- ========================================

INSERT INTO `recommend` (`bookId`, `rank`, `addTime`) VALUES
(1, 10, '2024-01-01 00:00:00'),  -- 红楼梦
(2, 9, '2024-01-01 00:00:00'),   -- Java核心技术
(3, 8, '2024-01-01 00:00:00'),   -- 算法导论
(5, 7, '2024-01-01 00:00:00'),   -- 西游记
(6, 6, '2024-01-01 00:00:00'),   -- 三国演义
(8, 5, '2024-01-01 00:00:00'),   -- 深入理解计算机系统
(10, 4, '2024-01-01 00:00:00');  -- 人工智能

-- ========================================
-- 新品推荐数据
-- ========================================

INSERT INTO `newproduct` (`bookId`, `rank`, `addTime`) VALUES
(10, 10, '2024-01-01 00:00:00'), -- 人工智能
(8, 9, '2024-01-01 00:00:00'),   -- 深入理解计算机系统
(4, 8, '2024-01-01 00:00:00'),   -- Spring Boot实战
(2, 7, '2024-01-01 00:00:00');   -- Java核心技术

-- ========================================
-- 公告数据
-- ========================================

INSERT INTO `announcement` (`title`, `content`, `author`, `publishTime`, `enable`) VALUES
('系统维护通知', '尊敬的读者：网站将于本周日 00:00-02:00 进行服务器维护，期间将暂停访问，敬请谅解。维护期间我们将优化系统性能，提升用户体验。', '<EMAIL>', '2024-01-01 09:00:00', 1),
('新书上架公告', '本月新上架 50 余种精品好书，涵盖文学、科技、教育等多个领域，欢迎大家选购！新书享受9折优惠，活动截止到月底。', '<EMAIL>', '2024-01-02 10:30:00', 1),
('春节放假通知', '春节期间（2月10日-2月17日）客服暂停服务，订单发货将延迟至2月18日恢复正常。祝大家新春快乐！', '<EMAIL>', '2024-01-25 14:00:00', 1),
('会员积分活动', '即日起至月底，会员购书可获得双倍积分，积分可用于兑换优惠券和精美礼品。详情请查看会员中心。', '<EMAIL>', '2024-01-15 16:20:00', 1),
('图书评价有奖', '参与图书评价活动，写下真实读后感，有机会获得精美书签和优惠券奖励！每月评选优秀评价10篇。', '<EMAIL>', '2024-01-10 11:45:00', 0);

-- ========================================
-- 网站介绍数据
-- ========================================

INSERT INTO `about` (`id`, `content`, `updateTime`) VALUES
(1, '智慧书店致力于为读者提供多元化、精品化的阅读体验。我们精选优质图书，提供每日特价、限时秒杀等优惠活动，让您尽享阅读乐趣。同时，我们还提供个性化推荐、书单分享、读者评价等服务，打造属于读者的温馨书香世界。无论您是文学爱好者、技术达人，还是学习进取者，都能在这里找到心仪的好书。', NOW());

-- ========================================
-- 更新出版社图书数量统计
-- ========================================

UPDATE `publish` SET `num` = (
    SELECT COUNT(*) FROM `book` WHERE `book`.`publish` = `publish`.`name`
) WHERE `id` > 0;

-- ========================================
-- 数据插入完成提示
-- ========================================

SELECT '========================================' as message
UNION ALL
SELECT '书店系统示例数据插入完成！' as message
UNION ALL
SELECT CONCAT('完成时间：', NOW()) as message
UNION ALL
SELECT '插入数据统计：' as message
UNION ALL
SELECT CONCAT('用户数量：', (SELECT COUNT(*) FROM user)) as message
UNION ALL
SELECT CONCAT('图书数量：', (SELECT COUNT(*) FROM book)) as message
UNION ALL
SELECT CONCAT('订单数量：', (SELECT COUNT(*) FROM bookorder)) as message
UNION ALL
SELECT CONCAT('评论数量：', (SELECT COUNT(*) FROM book_comment)) as message
UNION ALL
SELECT CONCAT('优惠券模板：', (SELECT COUNT(*) FROM coupon_template)) as message
UNION ALL
SELECT CONCAT('秒杀活动：', (SELECT COUNT(*) FROM spikeactivity)) as message
UNION ALL
SELECT CONCAT('书单数量：', (SELECT COUNT(*) FROM topic)) as message
UNION ALL
SELECT CONCAT('公告数量：', (SELECT COUNT(*) FROM announcement)) as message
UNION ALL
SELECT '========================================' as message;
